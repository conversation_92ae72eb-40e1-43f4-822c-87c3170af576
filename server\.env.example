# Server Configuration
NODE_ENV=development
PORT=5000
API_VERSION=v1

# Database Configuration
DATABASE_URL="postgresql://username:password@localhost:5432/agriconnect_db"

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-here
JWT_EXPIRES_IN=7d
JWT_REFRESH_SECRET=your-refresh-token-secret-here
JWT_REFRESH_EXPIRES_IN=30d

# Email Configuration (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
FROM_EMAIL=<EMAIL>
FROM_NAME=AgriConnect Cameroon

# SMS Configuration (Twilio or local provider)
SMS_PROVIDER=twilio
TWILIO_ACCOUNT_SID=your-twilio-account-sid
TWILIO_AUTH_TOKEN=your-twilio-auth-token
TWILIO_PHONE_NUMBER=+**********

# Weather API Configuration
WEATHER_API_KEY=your-openweathermap-api-key
WEATHER_API_URL=https://api.openweathermap.org/data/2.5

# File Upload Configuration
MAX_FILE_SIZE=5242880
UPLOAD_PATH=./uploads
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/gif,application/pdf

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# CORS Configuration
CORS_ORIGIN=http://localhost:3000

# Cameroon Specific Configuration
DEFAULT_CURRENCY=XAF
DEFAULT_LANGUAGE=fr
SUPPORTED_LANGUAGES=fr,en
DEFAULT_TIMEZONE=Africa/Douala

# External APIs
MARKET_PRICE_API_URL=https://api.example.com/market-prices
PAYMENT_GATEWAY_URL=https://api.payment-provider.cm
PAYMENT_API_KEY=your-payment-api-key

# Admin Configuration
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=change-this-password

# Logging
LOG_LEVEL=info
LOG_FILE=./logs/app.log

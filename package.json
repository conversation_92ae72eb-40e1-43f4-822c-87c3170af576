{"name": "agriconnect-cameroon", "version": "1.0.0", "description": "Comprehensive agricultural platform for Cameroon with multi-user support", "main": "server/index.js", "scripts": {"dev": "concurrently \"npm run server:dev\" \"npm run client:dev\"", "server:dev": "cd server && npm run dev", "client:dev": "cd client && npm run dev", "build": "npm run client:build && npm run server:build", "client:build": "cd client && npm run build", "server:build": "cd server && npm run build", "start": "cd server && npm start", "test": "npm run client:test && npm run server:test", "client:test": "cd client && npm test", "server:test": "cd server && npm test", "install:all": "npm install && cd client && npm install && cd ../server && npm install"}, "keywords": ["agriculture", "cameroon", "farming", "marketplace", "consultation", "multi-platform"], "author": "AgriConnect Team", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}
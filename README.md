# AgriConnect Cameroon

A comprehensive agricultural platform designed specifically for Cameroon's agricultural sector, featuring four distinct user platforms: Farmer, Seller, Expert, and Admin.

## 🌾 Project Overview

AgriConnect is a full-stack web application that addresses the unique needs of the Cameroonian agricultural ecosystem by providing:

- **Farmer Platform**: Crop management, market prices, weather forecasts, resource marketplace
- **Seller Platform**: Inventory management, product catalog, order fulfillment
- **Expert Platform**: Consultation services, knowledge sharing, advisory content
- **Admin Platform**: User management, analytics, content moderation

## 🇨🇲 Cameroon-Specific Features

- Support for major local crops: cocoa, coffee, plantains, cassava, maize, groundnuts, rice, yams
- Multi-language support: French (primary), English (secondary), with preparation for local languages
- Regional customization for all 10 provinces
- XAF currency integration
- SMS gateway integration for rural connectivity
- Offline-first architecture

## 🛠 Tech Stack

- **Frontend**: React/Next.js with TypeScript
- **Backend**: Node.js/Express
- **Database**: PostgreSQL with proper indexing
- **Mobile**: Progressive Web App (PWA)
- **Authentication**: JWT with role-based access control

## 🚀 Getting Started

### Prerequisites

- Node.js >= 18.0.0
- npm >= 8.0.0
- PostgreSQL >= 13

### Installation

1. Clone the repository
```bash
git clone <repository-url>
cd AgriConnect
```

2. Install all dependencies
```bash
npm run install:all
```

3. Set up environment variables
```bash
cp server/.env.example server/.env
cp client/.env.example client/.env
```

4. Set up the database
```bash
cd server
npm run db:setup
```

5. Start the development servers
```bash
npm run dev
```

## 📁 Project Structure

```
AgriConnect/
├── client/                 # Next.js frontend application
├── server/                 # Express.js backend API
├── shared/                 # Shared types and utilities
├── docs/                   # Documentation
└── scripts/                # Build and deployment scripts
```

## 🌍 Supported Regions

- Adamawa (Adamaoua)
- Centre
- East (Est)
- Far North (Extrême-Nord)
- Littoral
- North (Nord)
- Northwest (Nord-Ouest)
- South (Sud)
- Southwest (Sud-Ouest)
- West (Ouest)

## 📱 User Platforms

### Farmer Platform
- Farm registration and management
- Crop tracking and calendar
- Market price alerts
- Weather forecasts
- Resource marketplace
- Expert consultations

### Seller Platform
- Business registration
- Inventory management
- Product catalog (XAF pricing)
- Order management
- Customer communication
- Sales analytics

### Expert Platform
- Professional profiles
- Consultation scheduling
- Knowledge base
- Advisory content creation
- Performance metrics

### Admin Platform
- Multi-platform user management
- Content moderation
- System analytics
- Market data management
- Revenue monitoring

## 🔧 Development

### Running Tests
```bash
npm test
```

### Building for Production
```bash
npm run build
```

### Starting Production Server
```bash
npm start
```

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🤝 Contributing

Please read [CONTRIBUTING.md](CONTRIBUTING.md) for details on our code of conduct and the process for submitting pull requests.

## 📞 Support

For support, email <EMAIL> or join our Slack channel.

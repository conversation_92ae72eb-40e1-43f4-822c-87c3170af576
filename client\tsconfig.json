{"compilerOptions": {"target": "es5", "lib": ["dom", "dom.iterable", "es6"], "allowJs": true, "skipLibCheck": true, "strict": true, "forceConsistentCasingInFileNames": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@/components/*": ["./src/components/*"], "@/pages/*": ["./src/pages/*"], "@/hooks/*": ["./src/hooks/*"], "@/utils/*": ["./src/utils/*"], "@/types/*": ["./src/types/*"], "@/services/*": ["./src/services/*"], "@/store/*": ["./src/store/*"], "@/styles/*": ["./src/styles/*"], "@/public/*": ["./public/*"]}}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules"]}
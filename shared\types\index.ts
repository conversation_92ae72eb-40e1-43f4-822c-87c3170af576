// User Types
export enum UserRole {
  FARMER = 'farmer',
  SELLER = 'seller',
  EXPERT = 'expert',
  ADMIN = 'admin',
}

export enum UserStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  SUSPENDED = 'suspended',
  PENDING = 'pending',
}

export interface BaseUser {
  id: string;
  email: string;
  phone: string;
  firstName: string;
  lastName: string;
  role: UserRole;
  status: UserStatus;
  profileImage?: string;
  language: 'fr' | 'en';
  province: CameroonProvince;
  createdAt: Date;
  updatedAt: Date;
}

// Cameroon Specific Types
export enum CameroonProvince {
  ADAMAWA = 'adamawa',
  CENTRE = 'centre',
  EAST = 'east',
  FAR_NORTH = 'far_north',
  LITTORAL = 'littoral',
  NORTH = 'north',
  NORTHWEST = 'northwest',
  SOUTH = 'south',
  SOUTHWEST = 'southwest',
  WEST = 'west',
}

export enum CameroonCrop {
  COCOA = 'cocoa',
  COFFEE = 'coffee',
  PLANTAIN = 'plantain',
  CASSAVA = 'cassava',
  MAIZE = 'maize',
  GROUNDNUT = 'groundnut',
  RICE = 'rice',
  YAM = 'yam',
  COTTON = 'cotton',
  PALM_OIL = 'palm_oil',
  BANANA = 'banana',
  SWEET_POTATO = 'sweet_potato',
}

// Farmer Types
export interface FarmerProfile extends BaseUser {
  role: UserRole.FARMER;
  farmName: string;
  farmSize: number; // in hectares
  farmLocation: {
    latitude: number;
    longitude: number;
    address: string;
  };
  cropsGrown: CameroonCrop[];
  farmingExperience: number; // in years
  cooperativeMember: boolean;
  cooperativeName?: string;
}

export interface CropRecord {
  id: string;
  farmerId: string;
  crop: CameroonCrop;
  plantingDate: Date;
  expectedHarvestDate: Date;
  actualHarvestDate?: Date;
  areaPlanted: number; // in hectares
  expectedYield: number; // in kg
  actualYield?: number; // in kg
  status: 'planted' | 'growing' | 'harvested' | 'sold';
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
}

// Seller Types
export interface SellerProfile extends BaseUser {
  role: UserRole.SELLER;
  businessName: string;
  businessType: 'seeds' | 'fertilizers' | 'tools' | 'equipment' | 'general';
  businessLicense: string;
  businessAddress: string;
  businessPhone: string;
  businessEmail: string;
  verified: boolean;
  rating: number;
  totalSales: number;
}

export interface Product {
  id: string;
  sellerId: string;
  name: string;
  description: string;
  category: ProductCategory;
  price: number; // in XAF
  currency: 'XAF';
  images: string[];
  inStock: boolean;
  stockQuantity: number;
  unit: string; // kg, pieces, liters, etc.
  specifications?: Record<string, any>;
  tags: string[];
  createdAt: Date;
  updatedAt: Date;
}

export enum ProductCategory {
  SEEDS = 'seeds',
  FERTILIZERS = 'fertilizers',
  PESTICIDES = 'pesticides',
  TOOLS = 'tools',
  EQUIPMENT = 'equipment',
  IRRIGATION = 'irrigation',
  STORAGE = 'storage',
}

// Expert Types
export interface ExpertProfile extends BaseUser {
  role: UserRole.EXPERT;
  specialization: ExpertSpecialization[];
  credentials: string[];
  experience: number; // in years
  consultationRate: number; // per hour in XAF
  availability: ExpertAvailability;
  rating: number;
  totalConsultations: number;
  bio: string;
  languages: string[];
}

export enum ExpertSpecialization {
  CROP_MANAGEMENT = 'crop_management',
  PEST_CONTROL = 'pest_control',
  SOIL_MANAGEMENT = 'soil_management',
  IRRIGATION = 'irrigation',
  ORGANIC_FARMING = 'organic_farming',
  LIVESTOCK = 'livestock',
  AGRIBUSINESS = 'agribusiness',
  CLIMATE_ADAPTATION = 'climate_adaptation',
}

export interface ExpertAvailability {
  monday: TimeSlot[];
  tuesday: TimeSlot[];
  wednesday: TimeSlot[];
  thursday: TimeSlot[];
  friday: TimeSlot[];
  saturday: TimeSlot[];
  sunday: TimeSlot[];
}

export interface TimeSlot {
  start: string; // HH:mm format
  end: string; // HH:mm format
}

export interface Consultation {
  id: string;
  farmerId: string;
  expertId: string;
  title: string;
  description: string;
  scheduledDate: Date;
  duration: number; // in minutes
  status: 'scheduled' | 'in_progress' | 'completed' | 'cancelled';
  type: 'video' | 'phone' | 'in_person' | 'chat';
  price: number; // in XAF
  rating?: number;
  feedback?: string;
  createdAt: Date;
  updatedAt: Date;
}

// Market Types
export interface MarketPrice {
  id: string;
  crop: CameroonCrop;
  province: CameroonProvince;
  market: string;
  price: number; // per kg in XAF
  currency: 'XAF';
  date: Date;
  source: string;
  verified: boolean;
}

// Weather Types
export interface WeatherData {
  location: {
    latitude: number;
    longitude: number;
    name: string;
    province: CameroonProvince;
  };
  current: {
    temperature: number; // Celsius
    humidity: number; // percentage
    rainfall: number; // mm
    windSpeed: number; // km/h
    description: string;
    icon: string;
  };
  forecast: WeatherForecast[];
  lastUpdated: Date;
}

export interface WeatherForecast {
  date: Date;
  temperature: {
    min: number;
    max: number;
  };
  humidity: number;
  rainfall: number;
  description: string;
  icon: string;
}

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export interface PaginationParams {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  search?: string;
}

// Notification Types
export interface Notification {
  id: string;
  userId: string;
  title: string;
  message: string;
  type: 'info' | 'warning' | 'success' | 'error';
  category: 'weather' | 'market' | 'consultation' | 'order' | 'system';
  read: boolean;
  actionUrl?: string;
  createdAt: Date;
}

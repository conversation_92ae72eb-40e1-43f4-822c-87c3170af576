{"name": "agriconnect-client", "version": "1.0.0", "description": "Frontend application for AgriConnect Cameroon agricultural platform", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "type-check": "tsc --noEmit", "analyze": "cross-env ANALYZE=true next build"}, "dependencies": {"next": "^14.0.3", "react": "^18.2.0", "react-dom": "^18.2.0", "typescript": "^5.2.2", "@next/font": "^14.0.3", "next-pwa": "^5.6.0", "react-hook-form": "^7.47.0", "react-query": "^3.39.3", "axios": "^1.6.0", "zustand": "^4.4.6", "tailwindcss": "^3.3.5", "autoprefixer": "^10.4.16", "postcss": "^8.4.31", "@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "react-hot-toast": "^2.4.1", "date-fns": "^2.30.0", "react-datepicker": "^4.21.0", "react-select": "^5.8.0", "react-dropzone": "^14.2.3", "recharts": "^2.8.0", "leaflet": "^1.9.4", "react-leaflet": "^4.2.1", "next-i18next": "^15.1.0", "react-i18next": "^13.5.0", "i18next": "^23.7.6"}, "devDependencies": {"@types/node": "^20.8.10", "@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@types/leaflet": "^1.9.8", "@types/react-datepicker": "^4.19.4", "eslint": "^8.53.0", "eslint-config-next": "^14.0.3", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "jest": "^29.7.0", "@testing-library/react": "^13.4.0", "@testing-library/jest-dom": "^6.1.4", "jest-environment-jsdom": "^29.7.0", "@types/jest": "^29.5.8", "cross-env": "^7.0.3", "@next/bundle-analyzer": "^14.0.3"}, "keywords": ["agriculture", "nextjs", "react", "typescript", "cameroon", "pwa"], "author": "AgriConnect Team", "license": "MIT"}
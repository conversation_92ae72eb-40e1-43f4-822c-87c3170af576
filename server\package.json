{"name": "agriconnect-server", "version": "1.0.0", "description": "Backend API for AgriConnect Cameroon agricultural platform", "main": "dist/index.js", "scripts": {"dev": "nodemon src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "db:migrate": "npx prisma migrate dev", "db:generate": "npx prisma generate", "db:seed": "ts-node src/database/seeds/index.ts", "db:setup": "npm run db:generate && npm run db:migrate && npm run db:seed", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "dotenv": "^16.3.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "joi": "^17.11.0", "multer": "^1.4.5-lts.1", "nodemailer": "^6.9.7", "twilio": "^4.19.0", "axios": "^1.6.0", "prisma": "^5.6.0", "@prisma/client": "^5.6.0", "express-rate-limit": "^7.1.5", "compression": "^1.7.4", "express-validator": "^7.0.1"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/morgan": "^1.9.9", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5", "@types/multer": "^1.4.11", "@types/nodemailer": "^6.4.14", "@types/compression": "^1.7.5", "@types/node": "^20.8.10", "typescript": "^5.2.2", "nodemon": "^3.0.1", "ts-node": "^10.9.1", "jest": "^29.7.0", "@types/jest": "^29.5.8", "ts-jest": "^29.1.1", "supertest": "^6.3.3", "@types/supertest": "^2.0.16", "eslint": "^8.53.0", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0"}, "keywords": ["agriculture", "api", "cameroon", "express", "typescript"], "author": "AgriConnect Team", "license": "MIT"}